
COLOR_BG_HEADER_TXT_BLOCK = 'color_background_header_txt_block'
PAGE_NO = 'page-no'  # 页码
CONTENT_IN_FOOT_OR_HEADER = 'in-foot-header-area'  # 页眉页脚内的文本
VERTICAL_TEXT = 'vertical-text'  # 垂直文本
ROTATE_TEXT = 'rotate-text'  # 旋转文本
EMPTY_SIDE_BLOCK = 'empty-side-block'  # 边缘上的空白没有任何内容的block
ON_IMAGE_TEXT = 'on-image-text'  # 文本在图片上
ON_TABLE_TEXT = 'on-table-text'  # 文本在表格上


class DropTag:
    PAGE_NUMBER = 'page_no'
    HEADER = 'header'
    FOOTER = 'footer'
    FOOTNOTE = 'footnote'
    NOT_IN_LAYOUT = 'not_in_layout'
    SPAN_OVERLAP = 'span_overlap'
    BLOCK_OVERLAP = 'block_overlap'
