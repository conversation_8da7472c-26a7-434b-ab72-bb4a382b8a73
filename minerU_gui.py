import os
import time
import shutil
import json
import requests
import psycopg2
from pathlib import Path
from datetime import datetime
from typing import Optional, Tuple, List, Dict, Any

import gradio as gr
from loguru import logger

from magic_pdf.tools.common import do_parse, prepare_env
from magic_pdf.data.data_reader_writer import FileBasedDataReader
from magic_pdf.libs.hash_utils import compute_sha256


# 数据库配置
DB_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "database": "mineru_db",
    "user": "postgres",
    "password": "postgres"
}

# Ollama API 配置
OLLAMA_API = "http://localhost:11434/api"
OLLAMA_MODEL = "llama3"  # 默认模型，可以在界面中更改


def init_database():
    """初始化数据库连接和表结构"""
    try:
        conn = psycopg2.connect(
            host=DB_CONFIG["host"],
            port=DB_CONFIG["port"],
            database=DB_CONFIG["database"],
            user=DB_CONFIG["user"],
            password=DB_CONFIG["password"]
        )
        
        cursor = conn.cursor()
        
        # 创建文档表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS documents (
            id SERIAL PRIMARY KEY,
            file_name TEXT NOT NULL,
            original_path TEXT NOT NULL,
            output_path TEXT NOT NULL,
            md_content TEXT,
            file_hash TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            metadata JSONB
        )
        ''')
        
        # 创建处理记录表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS processing_records (
            id SERIAL PRIMARY KEY,
            document_id INTEGER REFERENCES documents(id),
            status TEXT NOT NULL,
            model_used TEXT,
            processing_time FLOAT,
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.info("数据库初始化成功")
        return True
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False


def check_config_file():
    """检查配置文件是否存在，如果不存在则创建"""
    user_home = str(Path.home())
    config_path = os.path.join(user_home, "magic-pdf.json")
    
    if not os.path.exists(config_path):
        logger.warning(f"配置文件不存在: {config_path}，将创建默认配置")
        
        # 基本配置内容
        config = {
            "bucket_info": {},
            "models-dir": os.path.join(user_home, "magic_pdf_models"),
            "layoutreader-model-dir": os.path.join(user_home, "magic_pdf_layoutreader"),
            "device-mode": "cpu",
            "layout-config": {
                "model": "doclayout_yolo"
            },
            "formula-config": {
                "mfd_model": "yolo_v8_mfd",
                "mfr_model": "unimernet_small",
                "enable": True
            },
            "table-config": {
                "model": "rapid_table",
                "sub_model": "slanet_plus",
                "enable": True,
                "max_time": 400
            },
            "latex-delimiter-config": {
                "display": {
                    "left": "$$",
                    "right": "$$"
                },
                "inline": {
                    "left": "$",
                    "right": "$"
                }
            },
            "ollama-config": {
                "api_url": OLLAMA_API,
                "default_model": OLLAMA_MODEL
            }
        }
        
        # 创建模型目录
        os.makedirs(config["models-dir"], exist_ok=True)
        os.makedirs(config["layoutreader-model-dir"], exist_ok=True)
        
        # 写入配置文件
        with open(config_path, "w") as f:
            json.dump(config, f, indent=4)
        
        logger.info(f"已创建配置文件: {config_path}")
        logger.warning("请确保下载必要的模型文件到配置的目录中")
        
        return False, f"已创建配置文件: {config_path}，但您需要下载模型文件才能使用转换功能。请运行 'python -m magic_pdf.tools.download_models' 下载模型。"
    
    return True, "配置文件检查通过"


def get_ollama_models():
    """获取可用的 Ollama 模型列表"""
    try:
        response = requests.get(f"{OLLAMA_API}/tags")
        if response.status_code == 200:
            models = response.json().get("models", [])
            return [model["name"] for model in models]
        return [OLLAMA_MODEL]
    except Exception as e:
        logger.error(f"获取 Ollama 模型列表失败: {e}")
        return [OLLAMA_MODEL]


def query_ollama(prompt: str, model: str = OLLAMA_MODEL) -> str:
    """使用 Ollama 模型进行查询"""
    try:
        response = requests.post(
            f"{OLLAMA_API}/generate",
            json={
                "model": model,
                "prompt": prompt,
                "stream": False
            }
        )
        
        if response.status_code == 200:
            return response.json().get("response", "")
        else:
            logger.error(f"Ollama API 请求失败: {response.status_code} {response.text}")
            return ""
    except Exception as e:
        logger.error(f"Ollama 查询失败: {e}")
        return ""


def enhance_markdown_with_ollama(md_content: str, model: str = OLLAMA_MODEL) -> str:
    """使用 Ollama 模型增强 Markdown 内容"""
    prompt = f"""
    请优化以下从PDF提取的Markdown内容，修复可能的格式问题，确保标题层级正确，
    表格格式正确，并保持原始内容的完整性。不要添加新内容，只修复格式问题：

    {md_content[:4000]}  # 限制长度以避免超出模型上下文窗口
    """
    
    try:
        enhanced_content = query_ollama(prompt, model)
        if enhanced_content:
            return enhanced_content
        return md_content
    except Exception as e:
        logger.error(f"Markdown 增强失败: {e}")
        return md_content


def save_to_database(file_path: str, output_dir: str, md_content: str, file_hash: str, metadata: Dict[str, Any]) -> int:
    """将处理结果保存到数据库"""
    try:
        conn = psycopg2.connect(
            host=DB_CONFIG["host"],
            port=DB_CONFIG["port"],
            database=DB_CONFIG["database"],
            user=DB_CONFIG["user"],
            password=DB_CONFIG["password"]
        )
        
        cursor = conn.cursor()
        
        # 插入文档记录
        cursor.execute(
            """
            INSERT INTO documents 
            (file_name, original_path, output_path, md_content, file_hash, metadata) 
            VALUES (%s, %s, %s, %s, %s, %s)
            RETURNING id
            """,
            (
                os.path.basename(file_path),
                file_path,
                output_dir,
                md_content,
                file_hash,
                json.dumps(metadata)
            )
        )
        
        document_id = cursor.fetchone()[0]
        
        # 插入处理记录
        cursor.execute(
            """
            INSERT INTO processing_records 
            (document_id, status, model_used, processing_time) 
            VALUES (%s, %s, %s, %s)
            """,
            (
                document_id,
                "success",
                metadata.get("layout_model", ""),
                metadata.get("processing_time", 0)
            )
        )
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.info(f"文档 {file_path} 已保存到数据库，ID: {document_id}")
        return document_id
    except Exception as e:
        logger.error(f"保存到数据库失败: {e}")
        return -1


def read_fn(path):
    """读取文件内容"""
    disk_rw = FileBasedDataReader(os.path.dirname(path))
    return disk_rw.read(os.path.basename(path))


def parse_file(
    file_path: str, 
    output_dir: str, 
    end_page_id: int, 
    is_ocr: bool, 
    layout_mode: str, 
    formula_enable: bool, 
    table_enable: bool, 
    language: str,
    use_ollama: bool = False,
    ollama_model: str = OLLAMA_MODEL
) -> Tuple[Optional[str], Optional[str], Optional[str]]:
    """处理单个文件"""
    os.makedirs(output_dir, exist_ok=True)
    
    start_time = time.time()
    
    try:
        # 检查配置文件
        config_ok, message = check_config_file()
        if not config_ok:
            logger.error(message)
            return None, None, None
            
        file_name = f'{str(Path(file_path).stem)}_{int(time.time())}'
        pdf_data = read_fn(file_path)
        file_hash = compute_sha256(pdf_data)
        
        parse_method = 'ocr' if is_ocr else 'auto'
        local_image_dir, local_md_dir = prepare_env(output_dir, file_name, parse_method)
        
        do_parse(
            output_dir,
            file_name,
            pdf_data,
            [],
            parse_method,
            False,
            end_page_id=end_page_id,
            layout_model=layout_mode,
            formula_enable=formula_enable,
            table_enable=table_enable,
            lang=language,
        )
        
        # 读取生成的 Markdown 文件
        md_path = os.path.join(local_md_dir, file_name + '.md')
        with open(md_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        # 使用 Ollama 增强 Markdown 内容
        if use_ollama:
            enhanced_md = enhance_markdown_with_ollama(md_content, ollama_model)
            # 保存增强后的内容
            enhanced_md_path = os.path.join(local_md_dir, file_name + '_enhanced.md')
            with open(enhanced_md_path, 'w', encoding='utf-8') as f:
                f.write(enhanced_md)
            md_content = enhanced_md
        
        processing_time = time.time() - start_time
        
        # 保存到数据库
        metadata = {
            "layout_model": layout_mode,
            "formula_enable": formula_enable,
            "table_enable": table_enable,
            "language": language,
            "is_ocr": is_ocr,
            "processing_time": processing_time,
            "use_ollama": use_ollama,
            "ollama_model": ollama_model if use_ollama else None
        }
        
        save_to_database(file_path, local_md_dir, md_content, file_hash, metadata)
        
        return local_md_dir, file_name, md_content
    except Exception as e:
        logger.exception(e)
        return None, None, None


def process_single_file(
    file_obj, 
    max_pages, 
    is_ocr, 
    layout_mode, 
    formula_enable, 
    table_enable, 
    language,
    use_ollama,
    ollama_model
):
    """处理单个上传的文件"""
    if file_obj is None:
        return "请先上传文件", None
    
    # 检查配置文件
    config_ok, message = check_config_file()
    if not config_ok:
        return message, None
    
    file_path = file_obj.name
    output_dir = "./output"
    
    local_md_dir, file_name, md_content = parse_file(
        file_path, 
        output_dir, 
        max_pages - 1, 
        is_ocr, 
        layout_mode, 
        formula_enable, 
        table_enable, 
        language,
        use_ollama,
        ollama_model
    )
    
    if local_md_dir is None:
        return "处理失败，请查看日志", None
    
    # 创建一个压缩包
    zip_path = os.path.join(output_dir, f"{file_name}.zip")
    shutil.make_archive(zip_path[:-4], 'zip', local_md_dir)
    
    return md_content, zip_path


def process_folder(
    folder_path, 
    max_pages, 
    is_ocr, 
    layout_mode, 
    formula_enable, 
    table_enable, 
    language,
    use_ollama,
    ollama_model
):
    """处理文件夹中的所有文件"""
    if not os.path.exists(folder_path):
        return "文件夹不存在", None
    
    # 检查配置文件
    config_ok, message = check_config_file()
    if not config_ok:
        return message, None
    
    output_dir = "./output/batch_" + str(int(time.time()))
    os.makedirs(output_dir, exist_ok=True)
    
    supported_extensions = ['.pdf', '.png', '.jpg', '.jpeg']
    processed_files = []
    failed_files = []
    
    for root, _, files in os.walk(folder_path):
        for file in files:
            file_path = os.path.join(root, file)
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext in supported_extensions:
                try:
                    local_md_dir, file_name, _ = parse_file(
                        file_path, 
                        output_dir, 
                        max_pages - 1, 
                        is_ocr, 
                        layout_mode, 
                        formula_enable, 
                        table_enable, 
                        language,
                        use_ollama,
                        ollama_model
                    )
                    
                    if local_md_dir is not None:
                        processed_files.append(file)
                    else:
                        failed_files.append(file)
                except Exception as e:
                    logger.exception(f"处理文件 {file} 时出错: {e}")
                    failed_files.append(file)
    
    # 创建一个包含所有结果的压缩包
    zip_path = os.path.join("./output", f"batch_results_{int(time.time())}.zip")
    shutil.make_archive(zip_path[:-4], 'zip', output_dir)
    
    result_text = f"处理完成！\n成功: {len(processed_files)} 个文件\n失败: {len(failed_files)} 个文件"
    if failed_files:
        result_text += f"\n\n失败的文件:\n" + "\n".join(failed_files)
    
    return result_text, zip_path


def search_documents(query: str, ollama_model: str = OLLAMA_MODEL) -> List[Dict[str, Any]]:
    """使用 Ollama 模型搜索数据库中的文档"""
    try:
        conn = psycopg2.connect(
            host=DB_CONFIG["host"],
            port=DB_CONFIG["port"],
            database=DB_CONFIG["database"],
            user=DB_CONFIG["user"],
            password=DB_CONFIG["password"]
        )
        
        cursor = conn.cursor()
        
        # 获取所有文档
        cursor.execute("SELECT id, file_name, md_content FROM documents")
        documents = cursor.fetchall()
        
        conn.close()
        
        if not documents:
            return []
        
        # 使用 Ollama 进行语义搜索
        results = []
        for doc_id, file_name, md_content in documents:
            # 构建提示词，询问文档与查询的相关性
            prompt = f"""
            请判断以下文档与查询"{query}"的相关性，返回0-10的分数，10表示最相关。
            只返回分数，不要有其他内容。
            
            文档内容：
            {md_content[:2000]}  # 限制长度
            """
            
            score_text = query_ollama(prompt, ollama_model)
            try:
                # 尝试提取分数
                score = float(score_text.strip())
            except:
                score = 0
            
            if score > 5:  # 只返回相关性较高的文档
                results.append({
                    "id": doc_id,
                    "file_name": file_name,
                    "score": score
                })
        
        # 按相关性排序
        results.sort(key=lambda x: x["score"], reverse=True)
        return results
    
    except Exception as e:
        logger.error(f"搜索文档失败: {e}")
        return []


def get_document_by_id(doc_id: int) -> Optional[Dict[str, Any]]:
    """根据ID获取文档详情"""
    try:
        conn = psycopg2.connect(
            host=DB_CONFIG["host"],
            port=DB_CONFIG["port"],
            database=DB_CONFIG["database"],
            user=DB_CONFIG["user"],
            password=DB_CONFIG["password"]
        )
        
        cursor = conn.cursor()
        
        cursor.execute(
            """
            SELECT d.id, d.file_name, d.original_path, d.output_path, 
                   d.md_content, d.created_at, d.metadata
            FROM documents d
            WHERE d.id = %s
            """,
            (doc_id,)
        )
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                "id": result[0],
                "file_name": result[1],
                "original_path": result[2],
                "output_path": result[3],
                "md_content": result[4],
                "created_at": result[5],
                "metadata": json.loads(result[6]) if result[6] else {}
            }
        return None
    
    except Exception as e:
        logger.error(f"获取文档失败: {e}")
        return None


def main():
    # 启动时检查配置文件
    config_ok, message = check_config_file()
    if not config_ok:
        logger.warning(message)
    
    # 初始化数据库
    db_ok = init_database()
    if not db_ok:
        logger.warning("数据库初始化失败，将无法保存处理记录")
    
    # 获取可用的 Ollama 模型
    ollama_models = get_ollama_models()
    
    with gr.Blocks(title="MinerU PDF/图片转换工具") as demo:
        gr.Markdown("# MinerU PDF/图片转换工具")
        gr.Markdown("支持PDF和图片转换为Markdown格式，集成Ollama模型增强和PostgreSQL数据库存储")
        
        if not config_ok:
            gr.Markdown(f"⚠️ **警告**: {message}", elem_classes=["warning-message"])
        
        if not db_ok:
            gr.Markdown("⚠️ **警告**: 数据库连接失败，将无法保存处理记录", elem_classes=["warning-message"])
        
        with gr.Tabs():
            with gr.Tab("单文件转换"):
                with gr.Row():
                    with gr.Column(scale=1):
                        file_input = gr.File(label="上传PDF或图片文件", file_types=[".pdf", ".png", ".jpg", ".jpeg"])
                        max_pages = gr.Slider(1, 50, 10, step=1, label="最大转换页数")
                        
                        with gr.Row():
                            layout_mode = gr.Dropdown(['doclayout_yolo'], label="布局模型", value='doclayout_yolo')
                            language = gr.Dropdown(['ch', 'en', 'auto'], label="语言", value='ch')
                        
                        with gr.Row():
                            formula_enable = gr.Checkbox(label="启用公式识别", value=True)
                            is_ocr = gr.Checkbox(label="强制启用OCR", value=False)
                            table_enable = gr.Checkbox(label="启用表格识别", value=True)
                        
                        convert_btn = gr.Button("开始转换")
                    
                    with gr.Column(scale=1):
                        output_md = gr.Markdown(label="Markdown预览")
                        output_file = gr.File(label="下载结果")
                
                convert_btn.click(
                    fn=process_single_file,
                    inputs=[file_input, max_pages, is_ocr, layout_mode, formula_enable, table_enable, language],
                    outputs=[output_md, output_file]
                )
            
            with gr.Tab("批量转换"):
                with gr.Row():
                    with gr.Column(scale=1):
                        folder_input = gr.Textbox(label="输入文件夹路径", placeholder="例如: /path/to/folder")
                        batch_max_pages = gr.Slider(1, 50, 10, step=1, label="每个文件最大转换页数")
                        
                        with gr.Row():
                            batch_layout_mode = gr.Dropdown(['doclayout_yolo'], label="布局模型", value='doclayout_yolo')
                            batch_language = gr.Dropdown(['ch', 'en', 'auto'], label="语言", value='ch')
                        
                        with gr.Row():
                            batch_formula_enable = gr.Checkbox(label="启用公式识别", value=True)
                            batch_is_ocr = gr.Checkbox(label="强制启用OCR", value=False)
                            batch_table_enable = gr.Checkbox(label="启用表格识别", value=True)
                        
                        batch_convert_btn = gr.Button("开始批量转换")
                    
                    with gr.Column(scale=1):
                        batch_result = gr.Textbox(label="处理结果", lines=10)
                        batch_output_file = gr.File(label="下载结果压缩包")
                
                batch_convert_btn.click(
                    fn=process_folder,
                    inputs=[folder_input, batch_max_pages, batch_is_ocr, batch_layout_mode, 
                            batch_formula_enable, batch_table_enable, batch_language],
                    outputs=[batch_result, batch_output_file]
                )

    demo.launch()


if __name__ == "__main__":
    main()

