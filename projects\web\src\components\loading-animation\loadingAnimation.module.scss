.loader {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  display: inline-block;
  position: relative;
  /* stylelint-disable-next-line alpha-value-notation */
  background: linear-gradient(0deg, rgba(13,83,222,1) 0%,  rgba(43,105,226,1) 30%,  rgba(13, 20, 222, 0) 100%);
  box-sizing: border-box;
  animation: rotation 1.5s linear infinite;
}

.loader::after {
  content: '';
  box-sizing: border-box;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #fff;
}

@keyframes rotation {
  0% { transform: rotate(0deg) }
  100% { transform: rotate(360deg)}
}
