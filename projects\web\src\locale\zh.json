{"extractor.common.upload": "点击上传文件", "extractor.common.try": "试一试：", "extractor.home": "首页", "extractor.button.download": "下载", "extractor.button.lineWrap": "换行", "extractor.button.fullScreen": "全屏", "extractor.button.exitFullScreen": "退出全屏", "extractor.button.showLayer": "显示识别结果", "extractor.button.hiddenLayer": "隐藏识别结果", "extractor.button.reUpload": "重新上传", "extractor.error": "提取失败", "extractor.common.loading": "加载中", "extractor.law": "请确保您上传的文件合法合规，我们不承担因文件内容产生的法律责任。《信息保护政策》 《儿童信息保护政策》《服务协议》|© All Rights Reserved.沪ICP备2021009351号-21", "extractor.failed": "不可提取，暂无可展示数据", "extractor.common.extracting": "提取中，请稍等", "extractor.common.extracting.queue": "正在排队提取，当前排在第 {id} 位", "extractor.common.pdf.demo1": "示例1.pdf", "extractor.common.pdf.demo2": "示例2.pdf", "extractor.common.formula.detect.demo1": "公式检测1.jpg", "extractor.common.formula.extract.demo1": "公式识别1.jpg", "extractor.common.login.desc": "登录后可使用完整功能", "extractor.markdown.preview": "预览", "extractor.markdown.code": "代码", "extractor.home.title": "欢迎使用 Miner U", "extractor.home.subTitle": "上传文档，智能提取为 Markdown 格式", "extractor.side.extractTask": "提取任务", "extractor.side.extractTask.title": "请上传 5M 以内的 PDF 文档 （ 10 页以内）或 JPG/PNG 图片", "extractor.pdf.title": "PDF文档提取", "extractor.pdf.subTitle": "支持文本/扫描型 PDF 解析，识别各类版面元素并转换为多模态 Markdown 格式", "extractor.common.pdf.upload.tip": "请上传 PDF 文档", "extractor.pdf.ocr": "OCR 识别模式", "extractor.pdf.ocr.popover": "  默认将自动识别PDF类型（文本型、扫描型），并根据识别结果选择采用文本识别或者OCR识别方式。 如开启，将对所有类型PDF采用OCR识别方式。", "extractor.formula.title": "定位图片中的行内、行间公式，生成边界框", "extractor.formula.title2": "将图片中的数学公式识别为 laTex 格式，支持多行公式、手写公式识别", "extractor.formula.upload.text": "点击上传图片", "extractor.formula.popover.extract": "为获得最佳的公式识别效果，请上传清晰、无水印的包含数学公式的图片，如下图", "extractor.formula.popover.detect": "为获得最佳的公式识别效果，请裁剪图片，聚焦公式部分，上传清晰、无水印的数学公式图片，如下图", "extractor.formula.upload.accept": "请上传 5M 以内的JPG/PNG 图片", "extractor.formula.upload.try": "请上传包含数学公式的图片 示例：", "extractor.guide.title": "欢迎使用更多开源产品 🎉", "extractor.queue": "提取记录", "extractor.queue.delete": "确认删除此文件？", "extractor.queue.extracting": "提取中", "extractor.feedback.title1": "您对整体提取效果是否满意 ？", "extractor.feedback.title3": "期待您的建议，帮助我们更好的优化", "extractor.feedback.up.title": "您期望看到哪些改进？", "extractor.feedback.down.title": "您感到不满意的原因是？", "extractor.feedback.up": "满意", "extractor.feedback.down": "不满意", "extractor.feedback.input.placeholder": "请输入您的改进建议", "extractor.feedback.input.submit": "提交", "extractor.feedback.success": "感谢你的反馈", "extractor.queue.delete.success": "删除成功"}